app.py

corr_matrix.py
    update_correlation_matrix

corr_quadrant.py
    update_quadrant_chart

corr_returns.py
    update_accumulated_returns
    local_minima
    local_maxima

debug_log.log

func_gfx.py
    build_weights_table
        normalize_weights
    create_combined_portfolio_string

func_mt5.py
    connect_mt5
    fetch_data
    calculate_returns_usd
    calculate_returns
    calculate_log_returns

func_portfolio.py
    _calculate_portfolio_correlation
        get_portfolio_series
    generate_portfolio_suggestions
    efficient_frontier_upper_hull
        cross
    identify_candidates_on_hull
    calculate_portfolio_correlation
    find_recommended_portfolios
    combine_portfolios
    process_custom_portfolio

func_rest.py
    cached_process_combo
    normalize_weights
    process_combo
    get_optimal_cores
    timing_decorator
        wrapper
    calculate_portfolio_metrics
    calculate_historical_metrics
    threshold_filter
    compute_rsi

layout.py

matrix23.py
    (Main application engine)

basket_analysis.py
    update_basket_analysis
    calculate_currency_strength
    generate_portfolio_recommendations

port_table.py
    toggle_portfolio_table

ratio_calcs.py
    portfolio_variance_numba
    portfolio_variance
    neg_sharpe_ratio_numba
    neg_sharpe_ratio
    downside_std_numba
    neg_sortino_ratio_numba
    neg_sortino_ratio
    compute_omega_ratio_numba
    compute_omega_ratio
    neg_calmar_ratio_numba
    neg_calmar_ratio
    compute_calmar_ratio_numba
    compute_calmar_ratio
    neg_total_return
    portfolio_volatility
    calculate_var_cvar
    calculate_var_cvar_numba
    neg_cvar_ratio
    compute_martin_ratio_numba
    compute_martin_ratio
    neg_martin_ratio
    neg_modified_sharpe_ratio_numba
    neg_modified_sharpe_ratio
