from dash import html, dcc
import plotly.graph_objects as go # Import plotly
layout = html.Div([
    html.H2("Portfolio Tracker", style={'color': 'white', 'marginTop': '20px'}),
    html.Div(id='mpt-weights-table', style={'marginBottom': '15px'}),  # New div for the weights table
    html.Div([
        dcc.Input(id='portfolio-allocation', type='text', 
                placeholder="Enter symbol:weight pairs e.g., EURUSD:0.4, GBPUSD:0.3", 
                style={'width': '60%'}),
        html.Button("Update MPT", id="update-mpt", n_clicks=0)
    ], style={'padding': '10px'}),
    html.Div([
        html.Label("Size:", style={'color': 'white', 'marginRight': '10px'}),
        dcc.Input(id='mpt-size', type='number', value=1.00, step=0.05, 
                 style={'width': '80px', 'marginRight': '15px'}),
        html.Button("Use Min Variance", id="use-minvar", n_clicks=0),
        html.Button("Use Max Sharpe", id="use-maxsharpe", n_clicks=0),
        html.Button("Use Max Sortino", id="use-maxsortino", n_clicks=0),
        html.Button("Use Max Omega", id="use-maxomega", n_clicks=0),
        html.Button("Use Max Calmar", id="use-maxcalmar", n_clicks=0),
        html.Button("Use Max CF Sharpe", id="use-maxcf", n_clicks=0),
        html.Button("Use Max Return", id="use-maxreturn", n_clicks=0),
        html.Button("Invert", id="use-invert", n_clicks=0),
        html.Span(" ", style={'margin': '0 15px'}),  # Small space
        html.Button("USD", id="use-usd", n_clicks=0),
        html.Button("EUR", id="use-eur", n_clicks=0),
        html.Button("GBP", id="use-gbp", n_clicks=0),
        html.Button("AUD", id="use-aud", n_clicks=0),
        html.Button("NZD", id="use-nzd", n_clicks=0),
        html.Button("CAD", id="use-cad", n_clicks=0),
        html.Button("CHF", id="use-chf", n_clicks=0),
        html.Button("JPY", id="use-jpy", n_clicks=0)
    ], style={'padding': '10px'}),
    dcc.Store(id='mpt-weights-store'),
    dcc.Store(id='optimization-running-store', data=False),
    dcc.Store(id='recommended-portfolios-store'),
    dcc.Store(id='recommended-cvar-portfolios-store'),
    dcc.Store(id='clicked-portfolio-store'), # Store for last clicked portfolio data
    dcc.Store(id='last-recommendation-button-store'), # Store for last recommendation button clicked
    dcc.Graph(id='mpt-tracker',
            figure=go.Figure(layout=go.Layout( # Add default figure with black background
                title="Portfolio Tracker",
                paper_bgcolor='black',
                plot_bgcolor='black',
                font=dict(color='white')
            ))
        ),
    dcc.Interval(id='interval-component1', interval=15000, n_intervals=0),

    # M15 chart for 7-trading-day data with multiple linear regression channels
    dcc.Graph(id='portfolio-m15-chart',
            figure=go.Figure(layout=go.Layout(
                title="Portfolio 7-Trading-Day M15 Data with Linear Regression Channels",
                paper_bgcolor='black',
                plot_bgcolor='black',
                font=dict(color='white')
            )),
            style={'height': '800px', 'marginTop': '20px'}),

    # MPT Tracker Weights (moved above basket analysis)
    html.Div([
        html.H4("MPT Tracker Weights", style={'marginBottom': '5px', 'color': 'white'}),
        dcc.Textarea(
            id='mpt-tracker-weights',
            readOnly=True,
            style={
                'width': '90%',
                'height': '110px',
                'backgroundColor': '#333',
                'color': 'white',
                'fontFamily': 'monospace',
                'fontSize': '18px',
                'border': '1px solid white',
                'padding': '10px',
                'lineHeight': '28px'
            }
        )
    ], style={'marginTop': '15px', 'marginBottom': '15px', 'padding': '10px', 'backgroundColor': 'black'}),

    # Currency Basket Analysis Table
    html.Div([
        html.H4("Currency Basket Analysis - RECOMMENDED TRADING PAIRS", style={'marginBottom': '10px', 'color': 'white'}),
        html.Div(id='basket-analysis-table', style={'marginBottom': '15px'}),
        dcc.Interval(id='basket-analysis-interval', interval=15000, n_intervals=0),

        # Three MPT Portfolio Textareas
        html.Div([
            html.H4("1. All Recommended Pairs (CF Sharpe Optimized):", style={'marginBottom': '5px', 'color': 'white', 'marginTop': '30px'}),
            dcc.Textarea(
                id='basket-portfolio-all',
                style={'width': '100%', 'height': '30px', 'backgroundColor': '#2d2d2d', 'color': 'white', 'border': '1px solid #555', 'fontFamily': 'monospace', 'fontSize': '18px'},
                readOnly=True
            ),

            html.H4("2. All Recommended Pairs (Normalized Only):", style={'marginBottom': '5px', 'color': 'white', 'marginTop': '30px'}),
            dcc.Textarea(
                id='basket-portfolio-filtered',
                style={'width': '100%', 'height': '30px', 'backgroundColor': '#2d2d2d', 'color': 'white', 'border': '1px solid #555', 'fontFamily': 'monospace', 'fontSize': '18px'},
                readOnly=True
            ),

            html.H4("3. Hierarchical Selection (CF Sharpe Optimized):", style={'marginBottom': '5px', 'color': 'white', 'marginTop': '30px'}),
            dcc.Textarea(
                id='basket-portfolio-hierarchical',
                style={'width': '100%', 'height': '30px', 'backgroundColor': '#2d2d2d', 'color': 'white', 'border': '1px solid #555', 'fontFamily': 'monospace', 'fontSize': '18px'},
                readOnly=True
            ),

            html.H4("4. Hierarchical Selection (Normalized Only):", style={'marginBottom': '5px', 'color': 'white', 'marginTop': '30px'}),
            dcc.Textarea(
                id='basket-portfolio-hierarchical-normalized',
                style={'width': '100%', 'height': '30px', 'backgroundColor': '#2d2d2d', 'color': 'white', 'border': '1px solid #555', 'fontFamily': 'monospace', 'fontSize': '18px'},
                readOnly=True
            )
        ], style={'marginTop': '15px'})
    ], style={'marginTop': '20px'}),

    # Persistent Portfolio Editbox
    html.Div([
        html.H4("My Trading Portfolios", style={'marginBottom': '5px', 'color': 'white'}),
        dcc.Textarea(
            id='persistent-portfolio-editbox',
            placeholder="Paste your MPT portfolios here (8-10 lines)...\nExample:\nEURUSD:0.4, GBPUSD:0.3, USDJPY:-0.3\nAUDUSD:0.5, NZDUSD:0.2, USDCAD:-0.7",
            style={
                'width': '90%',
                'height': '200px',
                'backgroundColor': '#333',
                'color': 'white',
                'fontFamily': 'monospace',
                'fontSize': '14px',
                'border': '1px solid white',
                'padding': '10px',
                'lineHeight': '20px',
                'resize': 'vertical'
            }
        ),
        html.Div([
            html.Button("Save Portfolios", id="save-portfolios-btn", n_clicks=0,
                       style={'backgroundColor': '#28a745', 'color': 'white', 'padding': '8px 16px',
                              'border': 'none', 'borderRadius': '4px', 'marginRight': '10px',
                              'fontSize': '14px', 'cursor': 'pointer'}),
            html.Span(id='save-status', style={'color': 'lightgreen', 'fontSize': '12px'})
        ], style={'marginTop': '10px'})
    ], style={'marginTop': '15px', 'marginBottom': '15px', 'padding': '10px', 'backgroundColor': 'black'}),

    # Trading Section
    html.H3("MT5 Trading", style={'color': 'white', 'marginTop': '20px'}),
    html.Div([
        # Account Info Display
        html.Div([
            html.H4("Account Information", style={'color': 'white', 'marginBottom': '10px'}),
            html.Div(id='account-info-display', style={
                'backgroundColor': '#333',
                'color': 'white',
                'padding': '10px',
                'border': '1px solid white',
                'borderRadius': '5px',
                'marginBottom': '15px',
                'fontFamily': 'monospace',
                'fontSize': '14px'
            })
        ]),

        # Trading Controls
        html.Div([
            html.Div([
                html.Label("Total Lot Size:", style={'color': 'white', 'marginRight': '10px'}),
                dcc.Input(
                    id='trading-lot-size',
                    type='number',
                    value=0.5,
                    min=0.01,
                    step=0.01,
                    style={'width': '100px', 'marginRight': '15px'}
                )
            ], style={'display': 'inline-block', 'marginRight': '20px'}),

            html.Div([
                html.Button("Send Trades to MT5",
                           id="send-trades-mt5",
                           n_clicks=0,
                           style={
                               'backgroundColor': '#28a745',
                               'color': 'white',
                               'padding': '10px 20px',
                               'border': 'none',
                               'borderRadius': '5px',
                               'fontSize': '16px',
                               'fontWeight': 'bold',
                               'cursor': 'pointer',
                               'marginRight': '10px'
                           }),
                html.Button("Close All Positions",
                           id="close-all-positions",
                           n_clicks=0,
                           style={
                               'backgroundColor': '#dc3545',
                               'color': 'white',
                               'padding': '10px 20px',
                               'border': 'none',
                               'borderRadius': '5px',
                               'fontSize': '16px',
                               'fontWeight': 'bold',
                               'cursor': 'pointer'
                           })
            ], style={'display': 'inline-block'})
        ], style={'marginBottom': '15px'}),

        # Trading Status Display
        html.Div(id='trading-status-display', style={
            'backgroundColor': '#333',
            'color': 'white',
            'padding': '10px',
            'border': '1px solid white',
            'borderRadius': '5px',
            'fontFamily': 'monospace',
            'fontSize': '14px',
            'minHeight': '50px'
        })
    ], style={'padding': '15px', 'backgroundColor': '#222', 'border': '1px solid #555', 'borderRadius': '10px', 'marginBottom': '20px'}),

    # Account update interval (1 second)
    dcc.Interval(id='account-interval', interval=1000, n_intervals=0),

    # Confirmation dialogs
    dcc.ConfirmDialog(
        id='confirm-send-trades',
        message='Are you sure you want to send trades to MT5 based on the selected portfolio allocation?',
    ),
    dcc.ConfirmDialog(
        id='confirm-close-positions',
        message='Are you sure you want to close all open MT5 positions?',
    ),



], style={'backgroundColor': 'black', 'color': 'white', 'padding': '20px'})
