# Portfolio Tracker System

## Project Overview & Purpose
Portfolio Tracker is an advanced forex portfolio analysis system that processes real-time data from MetaTrader5 to analyze currency correlations and provide comprehensive portfolio tracking through an interactive web dashboard. The system specializes in currency basket analysis, portfolio performance tracking, and correlation-based investment strategies.

## Key Features
- **Real-time Correlation Analysis**: Dynamic correlation matrices for currency pairs and portfolios
- **Currency Basket Analysis**: Intelligent currency strength analysis and pair recommendations
- **Portfolio Tracking**: Comprehensive portfolio performance monitoring with risk metrics
- **Interactive Visualization**: Real-time dashboard built with Plotly and Dash
- **Weekend Data Handling**: Intelligent caching system for weekend market closures
- **Numba-Optimized Calculations**: High-performance financial computations for real-time analysis
- **Adaptive Resource Management**: Dynamic CPU core allocation based on system load

## Technology Stack
- **Programming Language**: Python 3.10+ (64-bit required)
- **Core Libraries**:
    - `MetaTrader5==5.0.45`: Market data access and interaction
    - `dash==2.11.1`: Web dashboard framework
    - `pandas==2.1.4`: Data manipulation and analysis
    - `plotly==5.18.0`: Interactive charting and visualization
    - `numpy==1.24.3`: Numerical computing
    - `numba==0.58.1`: JIT compilation for performance optimization
    - `scipy==1.11.4`: Scientific computing and optimization
    - `joblib==1.3.2`: Parallel processing
- **Data Source**: MetaTrader5 Terminal

## Architecture
The system follows a modular architecture optimized for portfolio analysis and visualization:

```mermaid
graph TD
    MT5[MetaTrader5] --> |Raw Price Data| FUNC_MT5[func_mt5.py]
    FUNC_MT5 --> |Formatted Data| FUNC_PORTFOLIO[func_portfolio.py]
    FUNC_PORTFOLIO --> |Portfolio Metrics| RATIO_CALCS[ratio_calcs.py]
    RATIO_CALCS --> |Financial Ratios| MATRIX23[matrix23.py]
    MATRIX23 --> |Analysis Results| BASKET_ANALYSIS[Basket Analysis]
    BASKET_ANALYSIS --> |Portfolio Recommendations| LAYOUT[layout.py]
    LAYOUT --> |Dashboard Components| APP[app.py]
    WEEKEND_UTILS[weekend_utils.py] --> |Cache Management| FUNC_PORTFOLIO
    CORR_MODULES[Correlation Modules] --> |Visualizations| LAYOUT
    APP --> |Web Interface| USER[User via Browser]
```

### Key Components

#### Core Engine
- **[`matrix23.py`](matrix_f/matrix23.py:1)**: Main application engine with adaptive resource management and parallel processing
- **[`app.py`](matrix_f/app.py:1)**: Dash application initialization and server configuration
- **[`layout.py`](matrix_f/layout.py:1)**: Dashboard layout with correlation matrix, returns charts, and portfolio tables

#### Data Processing
- **[`func_mt5.py`](matrix_f/func_mt5.py:1)**: MetaTrader5 interface for data fetching and return calculations
- **[`func_portfolio.py`](matrix_f/func_portfolio.py:1)**: Portfolio correlation analysis and suggestion generation
- **[`func_rest.py`](matrix_f/func_rest.py:1)**: Utility functions with caching and performance optimization
- **[`func_gfx.py`](matrix_f/func_gfx.py:1)**: Visualization helper functions and weight table generation

#### Portfolio Analysis
- **[`ratio_calcs.py`](matrix_f/ratio_calcs.py:1)**: Numba-optimized financial ratio calculations (Sharpe, Sortino, Calmar, Omega, Martin ratios)
- **[`basket_analysis.py`](matrix_f/basket_analysis.py:1)**: Currency basket analysis and portfolio recommendations

#### Visualization Components
- **[`corr_matrix.py`](matrix_f/corr_matrix.py:1)**: Interactive correlation matrix visualization
- **[`corr_quadrant.py`](matrix_f/corr_quadrant.py:1)**: Quadrant-based correlation analysis charts
- **[`corr_returns.py`](matrix_f/corr_returns.py:1)**: Accumulated returns visualization with local minima/maxima detection
- **[`port_table.py`](matrix_f/port_table.py:1)**: Portfolio table component with toggle functionality

#### Utility Modules
- **[`weekend_utils.py`](matrix_f/weekend_utils.py:1)**: Weekend market closure handling and Friday data caching
- **[`market_phase.py`](matrix_f/market_phase.py:1)**: Market phase analysis and detection

## Prerequisites
- **Python**: Version 3.10 or higher, 64-bit installation
- **MetaTrader5 Terminal**: Must be installed, running, and logged into a broker account
- **System Requirements**: Multi-core CPU recommended for parallel optimization
- **Memory**: Minimum 8GB RAM for large portfolio calculations

## Installation
1. **Clone the repository:**
   ```bash
   git clone [repository-url]
   cd matrix_f
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify MetaTrader5 setup:**
   - Ensure MT5 terminal is running
   - Enable "Allow Algo Trading" in terminal settings
   - Verify broker connection and login

## Configuration
The system uses various configuration parameters embedded in the code:

### Key Parameters
- **Timeframes**: M1, M5, M15, M30, H1, H4, D1 (configurable via dropdown)
- **Data Hours**: Default 360 hours for correlation analysis
- **Return Hours**: Default 120 hours for return calculations
- **LR Channel Periods**: Default 240 periods for linear regression channels
- **Standard Deviation Threshold**: Configurable filtering (e.g., "<0.5", ">0.3")

### Portfolio Settings
- **Analysis Metrics**: Sharpe ratio, Sortino ratio, Calmar ratio, total return
- **Risk Metrics**: VaR, CVaR, maximum drawdown, volatility
- **Correlation Thresholds**: Customizable for portfolio diversification

## Usage Instructions
1. **Start the application:**
   ```bash
   python matrix23.py
   ```

2. **Access the dashboard:**
   - Open your web browser
   - Navigate to `http://localhost:8050` (default port)

3. **Configure analysis parameters:**
   - Set data hours for correlation analysis
   - Adjust return calculation periods
   - Select appropriate timeframe
   - Configure standard deviation thresholds

4. **Monitor portfolio performance:**
   - View real-time correlation matrices
   - Analyze accumulated returns
   - Review portfolio recommendations
   - Track performance metrics

## Input/Output

### Input
- **Real-time Market Data**: Live forex prices from MetaTrader5
- **Historical Data**: Configurable lookback periods for analysis
- **User Parameters**: Hours, thresholds, timeframes via dashboard controls
- **Weekend Cache**: Stored Friday data for weekend analysis

### Output
- **Interactive Dashboard**: Comprehensive portfolio analysis interface
- **Correlation Matrices**: Real-time currency pair correlation visualization
- **Portfolio Recommendations**: Currency basket-based allocation suggestions
- **Performance Metrics**: Sharpe, Sortino, Calmar ratios and other risk measures
- **Accumulated Returns**: Historical performance tracking with extrema detection
- **Quadrant Analysis**: Risk-return positioning charts

## Performance Optimization
- **Numba JIT Compilation**: Critical financial calculations optimized for speed
- **Parallel Processing**: Multi-core utilization for portfolio analysis
- **Adaptive Resource Management**: Dynamic CPU allocation based on system load
- **Intelligent Caching**: Weekend data persistence and validation
- **Memory Efficient**: Optimized data structures for large-scale calculations

## Error Handling & Logging
- **Comprehensive Logging**: INFO level logging for operations and debugging
- **MT5 Connection Monitoring**: Automatic reconnection and error handling
- **Data Validation**: Input parameter validation and sanitization
- **Weekend Mode**: Graceful handling of market closures
- **Resource Management**: CPU and memory usage monitoring

## Extension Points

### Advanced Analytics
1. **Machine Learning Integration**
   - Interface: [`func_portfolio.py`](matrix_f/func_portfolio.py:1)
   - Risk: Medium (model accuracy)
   - Validation: Backtesting framework required

2. **Alternative Risk Measures**
   - Interface: [`ratio_calcs.py`](matrix_f/ratio_calcs.py:1)
   - Risk: Low (supplementary metrics)
   - Validation: Historical comparison

3. **Multi-Asset Support**
   - Interface: [`func_mt5.py`](matrix_f/func_mt5.py:1)
   - Risk: Medium (data compatibility)
   - Validation: Cross-asset correlation analysis

### Trading Integration
1. **Automated Execution**
   - Interface: [`mt5_trading.py`](matrix_f/mt5_trading.py:1)
   - Risk: High (financial exposure)
   - Validation: Paper trading required

2. **Risk Management**
   - Interface: [`basket_analysis.py`](matrix_f/basket_analysis.py:1)
   - Risk: Medium (position sizing)
   - Validation: Stress testing needed

## Security Considerations

### Data Protection
- **Local Processing**: All calculations performed locally
- **MT5 Integration**: Read-only market data access
- **Cache Security**: Checksummed data validation
- **Network Security**: Local dashboard access only

### Risk Controls
- **Position Limits**: Configurable allocation constraints
- **Correlation Limits**: Diversification enforcement
- **Performance Monitoring**: Real-time risk metric tracking
- **Weekend Safeguards**: Market closure handling

## Performance Benchmarks
The system includes performance testing capabilities:
- **Benchmark Results**: Stored in [`benchmark_results.csv`](matrix_f/benchmark_results.csv:1) and [`benchmark_results.json`](matrix_f/benchmark_results.json:1)
- **Performance Testing**: Available via [`perf_test.py`](matrix_f/perf_test.py:1)
- **Optimization Metrics**: CPU usage, memory consumption, calculation speed

## Troubleshooting

### Common Issues
1. **MT5 Connection Errors**: Verify terminal is running and logged in
2. **Performance Issues**: Check CPU usage and adjust parallel processing
3. **Data Gaps**: Ensure continuous MT5 connection during market hours
4. **Weekend Behavior**: System automatically handles market closures

### Debug Tools
- **Debug Logging**: Available in [`debug_log.log`](matrix_f/debug_log.log:1)
- **Timezone Debugging**: Use [`debug_timezone.py`](matrix_f/debug_timezone.py:1)
- **Weekend Testing**: Run [`test_weekend_freeze.py`](matrix_f/test_weekend_freeze.py:1)

## Contributing
This project follows modular design principles. When contributing:
1. Maintain separation of concerns between modules
2. Add comprehensive tests for new functionality
3. Follow existing code patterns and documentation standards
4. Ensure compatibility with existing caching mechanisms

## License
This project is proprietary software. All rights reserved.

---
*For detailed API documentation, consult the docstrings within individual Python modules. For performance optimization guidance, refer to the benchmark results and performance testing utilities.*
