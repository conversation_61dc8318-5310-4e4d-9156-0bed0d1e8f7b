from app import app
import MetaTrader5 as mt5
import numpy as np
from numba import njit
import pandas as pd
from datetime import datetime, timedelta
import dash
from dash import html, Input, Output, State
import atexit
import plotly.graph_objects as go
import pytz
import scipy.optimize as sco
import json
import time
from joblib import Parallel, delayed
import os
import psutil
from itertools import combinations
import logging # Import logging module
import signal
import sys
import threading
import gc

from ratio_calcs import portfolio_variance
from func_mt5 import calculate_returns, fetch_data, convert_log_to_arithmetic_returns, connect_mt5
from func_rest import cached_process_combo, get_optimal_cores, timing_decorator
from func_gfx import create_combined_mpt_string
from func_portfolio import generate_portfolio_suggestions, find_recommended_portfolios, combine_portfolios
from annualization_utils import annualize_candidate_list, annualize_frontier_data, get_timeframe_description, format_return_percentage, format_risk_percentage
from weekend_utils import should_freeze_updates, should_use_friday_data, cache_friday_data, get_cached_friday_data, log_weekend_status
#from market_phase import generate_market_phase_figures # <-- Import the new function

# Import memory management system
try:
    from memory_manager import get_memory_manager, initialize_memory_management
    _use_memory_management = True
    print("Memory management system loaded successfully")
except ImportError as e:
    print(f"Warning: Could not load memory management system: {e}")
    _use_memory_management = False

import layout
import mpt_tracker
import mpt_trading_callbacks  # Import trading callbacks
import portfolio_callbacks  # Import portfolio persistence callbacks
import basket_analysis  # Import basket analysis functionality
#import matrix_phases

app.layout = layout.layout # Assign layout after all imports

# --- MetaTrader5 Initialization ---
# Initialize MT5 connection at startup
if not connect_mt5():
    print("WARNING: Failed to initialize MetaTrader5 connection!")
    print("Please ensure:")
    print("1. MetaTrader5 terminal is running")
    print("2. You are logged into a broker account")
    print("3. 'Allow Algo Trading' is enabled in terminal settings")
else:
    print("MetaTrader5 connection initialized successfully")

# Global variables for graceful shutdown
_shutdown_requested = False
_active_parallel_jobs = []
_shutdown_lock = threading.Lock()

def signal_handler(signum, frame):
    """Handle interrupt signals gracefully"""
    global _shutdown_requested

    with _shutdown_lock:
        if _shutdown_requested:
            print("\nForced shutdown requested. Terminating immediately...")
            sys.exit(1)

        _shutdown_requested = True
        print(f"\nShutdown signal received (signal {signum}). Initiating graceful shutdown...")
        print("Press Ctrl+C again to force immediate termination.")

        # Cancel active parallel jobs
        for job in _active_parallel_jobs:
            try:
                if hasattr(job, '_pool') and job._pool is not None:
                    print("Terminating parallel job pool...")
                    job._pool.terminate()
                    job._pool.join(timeout=5)
            except Exception as e:
                print(f"Error terminating parallel job: {e}")

        # Clear pandas caches to prevent export errors
        try:
            gc.collect()
            print("Memory cleanup completed")
        except Exception as e:
            print(f"Error during memory cleanup: {e}")

        # Cleanup MT5 connection
        cleanup_mt5()

        print("Graceful shutdown completed.")
        sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
if hasattr(signal, 'SIGTERM'):
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

# Register cleanup function to close MT5 connection on exit
def cleanup_mt5():
    """Clean up MetaTrader5 connection on application exit"""
    try:
        mt5.shutdown()
        print("MetaTrader5 connection closed")
    except Exception as e:
        print(f"Error closing MetaTrader5 connection: {e}")

atexit.register(cleanup_mt5)

def calculate_time_range_for_period_mpt(hours: int):
    """
    Calculate start and end times for MPT periods using matrix_QP-style logic

    Args:
        hours: Number of hours for the period (24, 72, 120, 240)

    Returns:
        Tuple of (start_time, end_time) in Europe/Bucharest timezone
    """
    MARKET_TIMEZONE = pytz.timezone('Europe/Bucharest')
    now = datetime.now(MARKET_TIMEZONE)

    # Calculate end time (current time)
    end_time = now

    # Handle different time periods
    if hours <= 24:
        # For periods <= 24h: from 00:00 today (current behavior)
        if now.weekday() >= 5:  # Weekend
            # Use Friday's data
            days_back = now.weekday() - 4  # Days back to Friday
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=days_back)
            end_time = start_time.replace(hour=23, minute=59, second=59)
        else:
            # Weekday: from 00:00 today
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
    else:
        # For periods > 24h: previous X weekdays (excluding weekends) + current day
        days = hours // 24

        # Start from current day and go back, counting only weekdays
        current_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
        weekdays_needed = days

        # If today is weekend, start from Friday
        if current_date.weekday() >= 5:  # Weekend
            days_to_friday = current_date.weekday() - 4
            current_date = current_date - timedelta(days=days_to_friday)
            end_time = current_date.replace(hour=23, minute=59, second=59)

        # Count back weekdays only
        weekdays_counted = 0
        check_date = current_date

        while weekdays_counted < weekdays_needed:
            check_date = check_date - timedelta(days=1)
            # Only count weekdays (Monday=0 to Friday=4)
            if check_date.weekday() < 5:
                weekdays_counted += 1

        start_time = check_date

        print(f"Multi-day period {hours}h: excluding weekends, using {weekdays_needed} weekdays")

    print(f"Time period {hours}h: {start_time} to {end_time}")
    print(f"Matrix_QP-style calculation: Weekend={now.weekday() >= 5}, Hours={hours}")
    return start_time, end_time

# --- Basic Logging Configuration ---
# Configure logging to output INFO level messages to the console
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__) # Optional: Get a logger for this specific module if needed

# Process chunks with controlled parallelism and CPU throttling
def get_adaptive_n_jobs():
    """Dynamically determine optimal number of cores based on current load and system resources"""
    import multiprocessing

    # Get system information
    max_cores = multiprocessing.cpu_count()
    current_cpu = psutil.cpu_percent(interval=0.1)
    memory = psutil.virtual_memory()
    available_memory_gb = memory.available / (1024**3)  # Convert to GB

    # Conservative core allocation based on CPU load
    if current_cpu > 85:
        cores_by_cpu = max(1, max_cores // 4)  # Use 25% of cores when very high
    elif current_cpu > 70:
        cores_by_cpu = max(2, max_cores // 3)  # Use 33% of cores when high
    elif current_cpu > 50:
        cores_by_cpu = max(2, max_cores // 2)  # Use 50% of cores when moderate
    else:
        cores_by_cpu = max(2, max_cores - 2)   # Leave 2 cores for system when normal

    # Memory-based core limitation (each core needs ~0.5GB for portfolio optimization)
    cores_by_memory = max(1, int(available_memory_gb / 0.5))

    # Use the more conservative limit
    optimal_cores = min(cores_by_cpu, cores_by_memory, max_cores - 1)

    print(f"System resources: CPU={current_cpu:.1f}%, Memory={available_memory_gb:.1f}GB available")
    print(f"Core allocation: CPU-limited={cores_by_cpu}, Memory-limited={cores_by_memory}, Selected={optimal_cores}")

    return optimal_cores
        
# --- Parallel Processing Configuration ---
# Create new Parallel instances for each operation to avoid conflicts
def create_parallel_executor(n_jobs=None):
    """Create a new Parallel instance for each operation to avoid conflicts"""
    global _shutdown_requested, _active_parallel_jobs

    # Check if shutdown was requested
    if _shutdown_requested:
        raise InterruptedError("Shutdown requested, cancelling parallel execution")

    if n_jobs is None:
        n_jobs = get_adaptive_n_jobs()

    parallel_job = Parallel(
        n_jobs=n_jobs,
        verbose=10,
        prefer="processes",
        backend="loky",
        batch_size="auto",
        max_nbytes='10M'
    )

    # Track active parallel jobs for cleanup
    with _shutdown_lock:
        _active_parallel_jobs.append(parallel_job)

    return parallel_job

def safe_parallel_execute(parallel_job, delayed_tasks):
    """Execute parallel tasks with proper cleanup and error handling"""
    global _shutdown_requested, _active_parallel_jobs

    try:
        # Check if shutdown was requested before starting
        if _shutdown_requested:
            raise InterruptedError("Shutdown requested, cancelling execution")

        # Execute the parallel tasks
        results = parallel_job(delayed_tasks)

        return results

    except (KeyboardInterrupt, InterruptedError) as e:
        print(f"Parallel execution interrupted: {e}")
        # Force cleanup of the parallel job
        try:
            if hasattr(parallel_job, '_pool') and parallel_job._pool is not None:
                parallel_job._pool.terminate()
                parallel_job._pool.join(timeout=2)
        except Exception as cleanup_error:
            print(f"Error during parallel job cleanup: {cleanup_error}")
        raise

    finally:
        # Remove from active jobs list
        with _shutdown_lock:
            if parallel_job in _active_parallel_jobs:
                _active_parallel_jobs.remove(parallel_job)


##################################################
#---MAIN (CACHE - MEMORY - CPU - TIMING)---#
# Set process priority to below normal to allow other system processes to run
try:
    process = psutil.Process(os.getpid())
    if os.name == 'nt':  # Windows
        process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
    else:  # Unix-based
        process.nice(10)  # Higher nice value = lower priority
except Exception as e:
    print(f"Error getting psutil process: {e}")
    pass

if __name__ == "__main__":
    atexit.register(mt5.shutdown)
    
    app.run(debug=True, port=7999, dev_tools_hot_reload=False)
    #serve(app.server, host='0.0.0.0', port=8053, threads=4, _quiet=True)